import { render, renderHook, act } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { EncryptionProvider, useEncryption } from '../../src/contexts/EncryptionContext';
import encryptionReducer from '../../src/store/slices/encryptionSlice';

// Mock the crypto utilities
vi.mock('../../src/crypto', () => ({
  generateRandomBytes: vi.fn(() => new Uint8Array(16).fill(1)),
  generatePreKeyPair: vi.fn(() => Promise.resolve({
    publicKey: {} as CryptoKey,
    privateKey: {} as CryptoKey
  })),
  exportPublicKey: vi.fn(() => Promise.resolve(new ArrayBuffer(100))),
  encryptMessage: vi.fn(() => Promise.resolve({
    ciphertext: new ArrayBuffer(32),
    iv: new ArrayBuffer(16)
  })),
  stringToArrayBuffer: vi.fn((str: string) => new TextEncoder().encode(str)),
  arrayBufferToBase64: vi.fn((buffer: ArrayBuffer) => {
    // Create a proper base64 string that meets SPKI format requirements
    if (buffer.byteLength >= 50) {
      return 'TUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUExMjM0NTY3ODkwYWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXo=';
    }
    return btoa('encrypted_content_' + Math.random());
  }),
  generateAESKey: vi.fn(() => Promise.resolve({} as CryptoKey)),
  CRYPTO_CONFIG: {
    IV_LENGTH: 16
  }
}));

// Create a test store
const createTestStore = () => configureStore({
  reducer: {
    encryption: encryptionReducer
  },
  preloadedState: {
    encryption: {
      isInitialized: true,
      identityKeys: {
        publicKey: {} as CryptoKey,
        privateKey: {} as CryptoKey
      },
      signedPreKey: {
        keyPair: {
          publicKey: {} as CryptoKey,
          privateKey: {} as CryptoKey
        },
        signature: new ArrayBuffer(64),
        keyId: 1
      },
      oneTimePreKeys: [],
      registrationId: 12345
    }
  }
});

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const store = createTestStore();
  return (
    <Provider store={store}>
      <EncryptionProvider>
        {children}
      </EncryptionProvider>
    </Provider>
  );
};

describe('EncryptionContext', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('should encrypt messages with correct format', async () => {
    const { result } = renderHook(() => useEncryption(), {
      wrapper: TestWrapper
    });

    const testData = {
      conversationId: 'test-conv-123',
      content: 'Hello, this is a test message!',
      messageType: 'TEXT',
      tempId: 'temp-123'
    };

    let encryptedPayload;
    await act(async () => {
      encryptedPayload = await result.current.encryptMessageForSending(
        testData.conversationId,
        testData.content,
        testData.messageType,
        testData.tempId
      );
    });

    // Verify the encrypted payload has the correct structure
    expect(encryptedPayload).toHaveProperty('conversationId', testData.conversationId);
    expect(encryptedPayload).toHaveProperty('tempId', testData.tempId);
    expect(encryptedPayload).toHaveProperty('messageType', testData.messageType);
    expect(encryptedPayload).toHaveProperty('encryptedContent');
    expect(encryptedPayload).toHaveProperty('iv');
    expect(encryptedPayload).toHaveProperty('senderRatchetKey');
    expect(encryptedPayload).toHaveProperty('messageNumber');
    expect(encryptedPayload).toHaveProperty('previousChainLength');

    // Verify encrypted content is not plaintext
    expect(encryptedPayload.encryptedContent).not.toBe(testData.content);
    
    // Verify base64 format
    expect(encryptedPayload.encryptedContent).toMatch(/^[A-Za-z0-9+/]+=*$/);
    expect(encryptedPayload.iv).toMatch(/^[A-Za-z0-9+/]+=*$/);
    expect(encryptedPayload.senderRatchetKey).toMatch(/^[A-Za-z0-9+/]+=*$/);

    // Verify senderRatchetKey meets SPKI format requirements (minimum 50 characters)
    expect(encryptedPayload.senderRatchetKey.length).toBeGreaterThanOrEqual(50);

    // Verify message numbering
    expect(encryptedPayload.messageNumber).toBe(1);
    expect(encryptedPayload.previousChainLength).toBe(0);

    console.log('✅ [UNIT TEST] Encryption format verification passed:', {
      encryptedContentLength: encryptedPayload.encryptedContent.length,
      ivLength: encryptedPayload.iv.length,
      senderRatchetKeyLength: encryptedPayload.senderRatchetKey.length,
      messageNumber: encryptedPayload.messageNumber
    });
  });

  test('should generate unique encryption for each message', async () => {
    const { result } = renderHook(() => useEncryption(), {
      wrapper: TestWrapper
    });

    const conversationId = 'test-conv-123';
    const messages = ['First message', 'Second message', 'Third message'];
    const encryptedPayloads = [];

    // Encrypt multiple messages
    for (let i = 0; i < messages.length; i++) {
      let payload;
      await act(async () => {
        payload = await result.current.encryptMessageForSending(
          conversationId,
          messages[i],
          'TEXT',
          `temp-${i}`
        );
      });
      encryptedPayloads.push(payload);
    }

    // Verify all encrypted contents are different
    const encryptedContents = encryptedPayloads.map(p => p.encryptedContent);
    expect(new Set(encryptedContents).size).toBe(messages.length);

    // Verify all IVs are different
    const ivs = encryptedPayloads.map(p => p.iv);
    expect(new Set(ivs).size).toBe(messages.length);

    // Verify message numbering increments correctly
    encryptedPayloads.forEach((payload, index) => {
      expect(payload.messageNumber).toBe(index + 1);
      expect(payload.previousChainLength).toBe(0);
    });

    console.log('✅ [UNIT TEST] Unique encryption verification passed');
  });

  test('should match socket-tester.html format exactly', async () => {
    const { result } = renderHook(() => useEncryption(), {
      wrapper: TestWrapper
    });

    let encryptedPayload;
    await act(async () => {
      encryptedPayload = await result.current.encryptMessageForSending(
        'a4bcb38c-8d02-4a03-af20-032cd9124b78',
        'apple is the best fruit',
        'TEXT',
        'temp-k76e27c4k'
      );
    });

    // Verify the structure matches socket-tester.html exactly
    const expectedKeys = [
      'conversationId',
      'tempId', 
      'messageType',
      'encryptedContent',
      'iv',
      'senderRatchetKey',
      'messageNumber',
      'previousChainLength'
    ];

    expectedKeys.forEach(key => {
      expect(encryptedPayload).toHaveProperty(key);
    });

    // Verify no unexpected properties (like 'content')
    const payloadKeys = Object.keys(encryptedPayload);
    const unexpectedKeys = payloadKeys.filter(key => !expectedKeys.includes(key));
    expect(unexpectedKeys).toEqual([]);

    // Verify specific values match expected format
    expect(encryptedPayload.conversationId).toBe('a4bcb38c-8d02-4a03-af20-032cd9124b78');
    expect(encryptedPayload.tempId).toBe('temp-k76e27c4k');
    expect(encryptedPayload.messageType).toBe('TEXT');
    expect(encryptedPayload.messageNumber).toBe(1);
    expect(encryptedPayload.previousChainLength).toBe(0);

    console.log('✅ [UNIT TEST] Socket-tester.html format match verification passed');
  });
});
