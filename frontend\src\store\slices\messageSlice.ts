// frontend/src/store/slices/messageSlice.ts
import { createSlice, createSelector } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';

export type MessageStatusType = 'SENT' | 'DELIVERED' | 'READ' | 'FAILED';

export interface MessageStatus {
  id: string;
  messageId: string;
  userId: string;
  status: MessageStatusType;
  createdAt: string;
  updatedAt: string;
}

export interface Message {
  id: string;
  conversationId: string;
  sender: {
    id: string;
    username: string;
    first_name?: string;
    last_name?: string;
    profile_picture?: string;
  };
  content: string;
  messageType: 'TEXT' | 'IMAGE' | 'FILE' | 'SYSTEM';
  createdAt: string;
  updatedAt: string;
  // Optional fields for optimistic updates
  tempId?: string;
  isOptimistic?: boolean;
  // Message status tracking
  status?: MessageStatusType;
  statuses?: MessageStatus[];
  // Encryption fields for encrypted messages from server
  encryptedContent?: string;
  iv?: string;
  senderRatchetKey?: string;
  messageNumber?: number;
  previousChainLength?: number;
  isEncrypted?: boolean;
}

export interface MessageState {
  messages: Record<string, Message[]>; // conversationId -> messages
  loading: boolean;
  error: string | null;
  sendingMessages: Record<string, boolean>; // tempId -> sending status
  optimisticMessageMap: Record<string, string>; // tempId -> realMessageId for correlation
  messageStatuses: Record<string, MessageStatusType>; // messageId -> status for current user
  failedMessages: Record<string, boolean>; // messageId -> failed status
  typingUsers: Record<string, string[]>; // conversationId -> userIds
}

const initialState: MessageState = {
  messages: {},
  loading: false,
  error: null,
  sendingMessages: {},
  optimisticMessageMap: {},
  messageStatuses: {},
  failedMessages: {},
  typingUsers: {}
};

// Helper function to sort messages by creation date
const sortMessagesByDate = (messages: Message[]): Message[] => {
  return [...messages].sort((a, b) =>
    new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
  );
};

const messageSlice = createSlice({
  name: 'messages',
  initialState,
  reducers: {
    // Socket event handlers
    addMessage: (state, action: PayloadAction<Message & { tempId?: string }>) => {
      const message = action.payload;

      console.log('🔴 [REDUX] Adding message to Redux store');
      console.log('🔴 [REDUX] Message ID:', message.id);
      console.log('🔴 [REDUX] TempId:', message.tempId);
      console.log('🔴 [REDUX] Conversation:', message.conversationId);
      console.log('🔴 [REDUX] Content:', message.content);

      if (!state.messages[message.conversationId]) {
        console.log('🔴 [REDUX] Creating new conversation array');
        state.messages[message.conversationId] = [];
      }

      const conversationMessages = state.messages[message.conversationId];
      console.log('🔴 [REDUX] Current conversation has', conversationMessages.length, 'messages');

      // Check if this message is replacing an optimistic message
      if (message.tempId) {
        console.log('🔴 [REDUX] Looking for optimistic message with tempId:', message.tempId);
        const optimisticIndex = conversationMessages.findIndex(msg => msg.id === message.tempId);
        console.log('🔴 [REDUX] Optimistic message index:', optimisticIndex);

        if (optimisticIndex !== -1) {
          console.log('🔴 [REDUX] ✅ Replacing optimistic message at index', optimisticIndex);
          // Replace optimistic message with real message
          conversationMessages[optimisticIndex] = {
            id: message.id,
            conversationId: message.conversationId,
            sender: message.sender,
            content: message.content,
            messageType: message.messageType,
            createdAt: message.createdAt,
            updatedAt: message.updatedAt
          };
          delete state.sendingMessages[message.tempId];
          // Sort messages after replacement
          state.messages[message.conversationId] = sortMessagesByDate(conversationMessages);
          console.log('🔴 [REDUX] ✅ Replaced optimistic message, conversation now has', conversationMessages.length, 'messages');
          return;
        } else {
          console.log('🔴 [REDUX] ⚠️ Optimistic message not found for tempId:', message.tempId);
        }
      }

      // Check for duplicates by ID
      console.log('🔴 [REDUX] Checking for existing message with ID:', message.id);
      const existingIndex = conversationMessages.findIndex(msg => msg.id === message.id);
      console.log('🔴 [REDUX] Existing message index:', existingIndex);

      if (existingIndex === -1) {
        console.log('🔴 [REDUX] ✅ Adding new message to Redux');
        // Add new message and sort
        conversationMessages.push({
          id: message.id,
          conversationId: message.conversationId,
          sender: message.sender,
          content: message.content,
          messageType: message.messageType,
          createdAt: message.createdAt,
          updatedAt: message.updatedAt
        });
        state.messages[message.conversationId] = sortMessagesByDate(conversationMessages);
        console.log('🔴 [REDUX] ✅ Redux conversation now has', conversationMessages.length, 'messages');
      } else {
        console.log('🔴 [REDUX] ⚠️ Message already exists in Redux, skipping duplicate');
      }
    },

    addOptimisticMessage: (state, action: PayloadAction<{
      tempId: string;
      message: Message;
    }>) => {
      const { tempId, message } = action.payload;

      if (!state.messages[message.conversationId]) {
        state.messages[message.conversationId] = [];
      }

      // Add optimistic message with temp ID
      const optimisticMessage: Message = {
        ...message,
        id: tempId,
      };

      state.messages[message.conversationId].push(optimisticMessage);
      state.messages[message.conversationId] = sortMessagesByDate(state.messages[message.conversationId]);
      state.sendingMessages[tempId] = true;
      state.optimisticMessageMap[tempId] = tempId;
    },

    removeOptimisticMessage: (state, action: PayloadAction<string>) => {
      const tempId = action.payload;

      // Find and remove the optimistic message
      for (const conversationId in state.messages) {
        const messages = state.messages[conversationId];
        const index = messages.findIndex(msg => msg.id === tempId);
        if (index !== -1) {
          messages.splice(index, 1);
          break;
        }
      }

      delete state.sendingMessages[tempId];
      delete state.optimisticMessageMap[tempId];
    },

    updateOptimisticMessage: (state, action: PayloadAction<{
      tempId: string;
      realMessage: Message;
    }>) => {
      const { tempId, realMessage } = action.payload;

      // Find and replace the optimistic message
      const conversationMessages = state.messages[realMessage.conversationId];
      if (conversationMessages) {
        const index = conversationMessages.findIndex(msg => msg.id === tempId);
        if (index !== -1) {
          conversationMessages[index] = realMessage;
          state.messages[realMessage.conversationId] = sortMessagesByDate(conversationMessages);
        }
      }

      delete state.sendingMessages[tempId];
      state.optimisticMessageMap[tempId] = realMessage.id;
    },

    setTypingUsers: (state, action: PayloadAction<{
      conversationId: string;
      userIds: string[];
    }>) => {
      const { conversationId, userIds } = action.payload;
      state.typingUsers[conversationId] = userIds;
    },

    addTypingUser: (state, action: PayloadAction<{
      conversationId: string;
      userId: string;
    }>) => {
      const { conversationId, userId } = action.payload;

      if (!state.typingUsers[conversationId]) {
        state.typingUsers[conversationId] = [];
      }

      const typingUsers = state.typingUsers[conversationId];
      if (!typingUsers.includes(userId)) {
        typingUsers.push(userId);
      }
    },

    removeTypingUser: (state, action: PayloadAction<{
      conversationId: string;
      userId: string;
    }>) => {
      const { conversationId, userId } = action.payload;

      if (state.typingUsers[conversationId]) {
        const typingUsers = state.typingUsers[conversationId];
        const userIndex = typingUsers.indexOf(userId);
        if (userIndex !== -1) {
          typingUsers.splice(userIndex, 1);
        }
      }
    },

    setSendingMessage: (state, action: PayloadAction<string>) => {
      const tempId = action.payload;
      state.sendingMessages[tempId] = true;
    },

    removeSendingMessage: (state, action: PayloadAction<string>) => {
      const tempId = action.payload;
      delete state.sendingMessages[tempId];
    },

    clearMessages: (state, action: PayloadAction<string>) => {
      const conversationId = action.payload;
      delete state.messages[conversationId];
    },

    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },

    updateMessageStatus: (state, action: PayloadAction<{
      messageId: string;
      status: MessageStatusType;
      tempId?: string;
    }>) => {
      const { messageId, status, tempId } = action.payload;

      // Update status in messageStatuses map
      state.messageStatuses[messageId] = status;

      // Remove from failed messages if status is not FAILED
      if (status !== 'FAILED') {
        delete state.failedMessages[messageId];
      } else {
        state.failedMessages[messageId] = true;
      }

      // If tempId is provided, update optimistic message map
      if (tempId) {
        state.optimisticMessageMap[tempId] = messageId;
      }
    },

    markMessageAsFailed: (state, action: PayloadAction<{
      messageId?: string;
      tempId?: string;
    }>) => {
      const { messageId, tempId } = action.payload;

      if (messageId) {
        state.messageStatuses[messageId] = 'FAILED';
        state.failedMessages[messageId] = true;
      }

      if (tempId) {
        // Remove from sending messages
        delete state.sendingMessages[tempId];
      }
    },

    retryMessage: (state, action: PayloadAction<{
      messageId: string;
      tempId?: string;
    }>) => {
      const { messageId, tempId } = action.payload;

      // Remove from failed messages
      delete state.failedMessages[messageId];

      // Reset status to SENT (will be updated to DELIVERED when successful)
      state.messageStatuses[messageId] = 'SENT';

      // Set status back to sending if tempId provided
      if (tempId) {
        state.sendingMessages[tempId] = true;
      }
    },

    clearMessageError: (state, action: PayloadAction<string>) => {
      const messageId = action.payload;
      delete state.failedMessages[messageId];
      delete state.messageStatuses[messageId];
    }
  }
});

export const {
  addMessage,
  addOptimisticMessage,
  removeOptimisticMessage,
  updateOptimisticMessage,
  setTypingUsers,
  addTypingUser,
  removeTypingUser,
  setSendingMessage,
  removeSendingMessage,
  clearMessages,
  setError,
  updateMessageStatus,
  markMessageAsFailed,
  retryMessage,
  clearMessageError
} = messageSlice.actions;

export default messageSlice.reducer;

// ============================================================================
// Memoized Selectors
// ============================================================================

// Base selectors
const selectMessageState = (state: { messages: MessageState }) => state.messages;
const selectMessagesMap = (state: { messages: MessageState }) => state.messages.messages;
const selectConversationId = (_: any, conversationId: string) => conversationId;

// Memoized selector for messages by conversation ID
export const selectMessagesByConversation = createSelector(
  [selectMessagesMap, selectConversationId],
  (messagesMap, conversationId) => messagesMap[conversationId] || []
);

// Memoized selector for sorted messages by conversation ID
export const selectSortedMessagesByConversation = createSelector(
  [selectMessagesByConversation],
  (messages) => {
    if (messages.length === 0) return messages;
    return [...messages].sort((a, b) =>
      new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    );
  }
);

// Memoized selector for typing users by conversation ID
export const selectTypingUsersByConversation = createSelector(
  [
    (state: { messages: MessageState }) => state.messages.typingUsers,
    (_: any, conversationId: string) => conversationId
  ],
  (typingUsers, conversationId) => typingUsers[conversationId] || []
);

// Other memoized selectors for common use cases
export const selectSendingMessages = createSelector(
  [selectMessageState],
  (messageState) => messageState.sendingMessages || {}
);

export const selectMessageStatuses = createSelector(
  [selectMessageState],
  (messageState) => messageState.messageStatuses || {}
);

export const selectFailedMessages = createSelector(
  [selectMessageState],
  (messageState) => messageState.failedMessages || {}
);

export const selectTypingUsers = createSelector(
  [selectMessageState],
  (messageState) => messageState.typingUsers || {}
);
