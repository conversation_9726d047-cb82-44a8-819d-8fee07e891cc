// <PERSON><PERSON>er console test script to verify encryption implementation
// Run this in the browser console after logging in and navigating to a conversation

console.log('🔍 [ENCRYPTION TEST] Starting encryption verification...');

// Test 1: Verify EncryptionContext is available
try {
  // Check if the encryption context is properly integrated
  const encryptionReady = window.React && window.React.useContext;
  console.log('✅ [TEST 1] React context system available:', !!encryptionReady);
} catch (error) {
  console.error('❌ [TEST 1] React context system not available:', error);
}

// Test 2: Intercept socket messages to verify encryption
let capturedMessages = [];

// Override the socket emit function to capture messages
if (window.io && window.io.sockets) {
  const originalEmit = window.io.sockets[0]?.emit;
  if (originalEmit) {
    window.io.sockets[0].emit = function(event, data) {
      if (event === 'send_message') {
        capturedMessages.push(data);
        console.log('🔍 [TEST 2] Captured socket message:', data);
        
        // Verify encryption format
        if (data.encryptedContent && data.iv && data.senderRatchetKey) {
          console.log('✅ [TEST 2] Message has encrypted format');
          console.log('  - encryptedContent length:', data.encryptedContent.length);
          console.log('  - iv length:', data.iv.length);
          console.log('  - senderRatchetKey length:', data.senderRatchetKey.length);
          console.log('  - messageNumber:', data.messageNumber);
          console.log('  - previousChainLength:', data.previousChainLength);
          
          // Verify senderRatchetKey meets SPKI format requirements
          if (data.senderRatchetKey.length >= 50) {
            console.log('✅ [TEST 2] senderRatchetKey meets SPKI format requirements');
          } else {
            console.error('❌ [TEST 2] senderRatchetKey too short:', data.senderRatchetKey.length);
          }
          
          // Verify no plaintext content
          if (!data.content) {
            console.log('✅ [TEST 2] No plaintext content found');
          } else {
            console.error('❌ [TEST 2] Plaintext content found:', data.content);
          }
        } else if (data.content) {
          console.error('❌ [TEST 2] Message sent as plaintext:', data);
        } else {
          console.warn('⚠️ [TEST 2] Unknown message format:', data);
        }
      }
      return originalEmit.call(this, event, data);
    };
    console.log('✅ [TEST 2] Socket message interception set up');
  } else {
    console.error('❌ [TEST 2] Socket not found or not connected');
  }
}

// Test 3: Manual encryption test
console.log('🔍 [TEST 3] Manual encryption test instructions:');
console.log('1. Type a message in the chat input');
console.log('2. Click send');
console.log('3. Check the console output above for encryption verification');
console.log('4. Expected: Message should have encryptedContent, iv, senderRatchetKey, etc.');
console.log('5. Expected: No plaintext "content" field should be present');

// Test 4: Compare with socket-tester.html format
const expectedFormat = {
  conversationId: 'string',
  tempId: 'string',
  messageType: 'TEXT|IMAGE|FILE|SYSTEM',
  encryptedContent: 'base64 string',
  iv: 'base64 string',
  senderRatchetKey: 'base64 string (min 50 chars)',
  messageNumber: 'number',
  previousChainLength: 'number'
};

console.log('🔍 [TEST 4] Expected message format (from socket-tester.html):');
console.log(expectedFormat);

// Helper function to verify message format
window.verifyEncryptionFormat = function(message) {
  const requiredKeys = Object.keys(expectedFormat);
  const messageKeys = Object.keys(message);
  
  console.log('🔍 [VERIFY] Checking message format...');
  
  // Check all required keys are present
  const missingKeys = requiredKeys.filter(key => !messageKeys.includes(key));
  if (missingKeys.length > 0) {
    console.error('❌ [VERIFY] Missing required keys:', missingKeys);
    return false;
  }
  
  // Check no unexpected keys (like 'content')
  const unexpectedKeys = messageKeys.filter(key => !requiredKeys.includes(key));
  if (unexpectedKeys.length > 0) {
    console.error('❌ [VERIFY] Unexpected keys found:', unexpectedKeys);
    return false;
  }
  
  // Check senderRatchetKey length
  if (message.senderRatchetKey.length < 50) {
    console.error('❌ [VERIFY] senderRatchetKey too short:', message.senderRatchetKey.length);
    return false;
  }
  
  console.log('✅ [VERIFY] Message format is correct!');
  return true;
};

console.log('🔍 [ENCRYPTION TEST] Verification setup complete!');
console.log('📝 [INSTRUCTIONS] Now send a message through the UI and check the console output.');
console.log('📝 [INSTRUCTIONS] You can also call window.verifyEncryptionFormat(capturedMessages[0]) to verify the format.');
