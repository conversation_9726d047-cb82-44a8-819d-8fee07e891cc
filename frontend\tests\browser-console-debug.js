// Browser Console Debug Script for Message Flow Issues
// Run this in the browser console after logging in and navigating to a conversation

console.log('🚀 [DEBUG] Starting Message Flow Debug Script...');

// Global variables to track state
window.debugState = {
  outgoingMessages: [],
  incomingMessages: [],
  reduxStateChanges: [],
  uiMessages: [],
  startTime: Date.now()
};

// Function to log with timestamp
function debugLog(category, message, data = null) {
  const timestamp = Date.now() - window.debugState.startTime;
  const logMessage = `[${timestamp}ms] ${category}: ${message}`;
  console.log(logMessage, data || '');
}

// 1. Monitor Redux State Changes
function monitorReduxState() {
  if (!window.store) {
    console.error('❌ Redux store not found on window.store');
    return;
  }

  let lastState = null;
  
  const checkState = () => {
    const currentState = window.store.getState();
    const messages = currentState.messages?.messages || {};
    const sendingMessages = currentState.messages?.sendingMessages || {};
    const optimisticMessageMap = currentState.messages?.optimisticMessageMap || {};
    
    const stateSnapshot = {
      totalMessages: Object.values(messages).reduce((sum, msgs) => sum + (msgs?.length || 0), 0),
      conversationCount: Object.keys(messages).length,
      sendingCount: Object.keys(sendingMessages).length,
      optimisticCount: Object.keys(optimisticMessageMap).length,
      messages: JSON.parse(JSON.stringify(messages))
    };
    
    if (!lastState || JSON.stringify(stateSnapshot) !== JSON.stringify(lastState)) {
      debugLog('REDUX', 'State changed', stateSnapshot);
      window.debugState.reduxStateChanges.push({
        timestamp: Date.now(),
        state: stateSnapshot
      });
      lastState = stateSnapshot;
    }
  };
  
  // Check state every 100ms
  setInterval(checkState, 100);
  debugLog('REDUX', 'State monitoring started');
}

// 2. Monitor Socket Messages
function monitorSocketMessages() {
  // Find socket connection
  let socket = null;
  
  if (window.io && window.io.sockets && window.io.sockets[0]) {
    socket = window.io.sockets[0];
  } else {
    console.error('❌ Socket not found');
    return;
  }
  
  // Override emit to capture outgoing messages
  const originalEmit = socket.emit;
  socket.emit = function(event, data) {
    if (event === 'send_message') {
      debugLog('SOCKET OUT', 'send_message', data);
      window.debugState.outgoingMessages.push({
        timestamp: Date.now(),
        event,
        data: JSON.parse(JSON.stringify(data))
      });
      
      // Check if message is encrypted
      const isEncrypted = !!(data.encryptedContent && data.iv && data.senderRatchetKey);
      debugLog('ENCRYPTION', `Outgoing message encrypted: ${isEncrypted}`);
      
      if (isEncrypted) {
        debugLog('ENCRYPTION', 'Encryption details', {
          encryptedContentLength: data.encryptedContent.length,
          ivLength: data.iv.length,
          senderRatchetKeyLength: data.senderRatchetKey.length,
          messageNumber: data.messageNumber,
          previousChainLength: data.previousChainLength
        });
      }
      
      if (data.content) {
        console.warn('⚠️ [ENCRYPTION] Plaintext content found in outgoing message:', data.content);
      }
    }
    return originalEmit.call(this, event, data);
  };
  
  // Override addEventListener to capture incoming messages
  const originalAddEventListener = socket.addEventListener;
  socket.addEventListener = function(event, handler) {
    if (event === 'message') {
      const wrappedHandler = function(messageEvent) {
        try {
          const data = messageEvent.data;
          if (typeof data === 'string') {
            const match = data.match(/\["([^"]+)",(.+)\]/);
            if (match) {
              const [, eventName, payload] = match;
              const parsedPayload = JSON.parse(payload);
              
              if (eventName === 'new_message') {
                debugLog('SOCKET IN', 'new_message', parsedPayload);
                window.debugState.incomingMessages.push({
                  timestamp: Date.now(),
                  event: eventName,
                  data: JSON.parse(JSON.stringify(parsedPayload))
                });
                
                // Check encryption status
                const isEncrypted = parsedPayload.isEncrypted;
                debugLog('ENCRYPTION', `Incoming message encrypted: ${isEncrypted}`);
                
                if (isEncrypted && parsedPayload.encryptedContent) {
                  debugLog('ENCRYPTION', 'Encrypted message received', {
                    hasEncryptedContent: !!parsedPayload.encryptedContent,
                    hasIv: !!parsedPayload.iv,
                    hasSenderRatchetKey: !!parsedPayload.senderRatchetKey
                  });
                }
                
                if (parsedPayload.content) {
                  debugLog('DECRYPTION', 'Message has content field', {
                    contentLength: parsedPayload.content.length,
                    contentPreview: parsedPayload.content.substring(0, 50)
                  });
                }
              }
            }
          }
        } catch (e) {
          debugLog('SOCKET IN', 'Parse error', e.message);
        }
        return handler(messageEvent);
      };
      return originalAddEventListener.call(this, event, wrappedHandler);
    }
    return originalAddEventListener.call(this, event, handler);
  };
  
  debugLog('SOCKET', 'Socket monitoring started');
}

// 3. Monitor UI Messages
function monitorUIMessages() {
  const checkUIMessages = () => {
    const messageElements = document.querySelectorAll('[data-testid="message"]');
    const currentMessages = Array.from(messageElements).map((el, index) => {
      const content = el.textContent?.trim() || '';
      const messageId = el.getAttribute('data-message-id') || `ui-${index}`;
      const senderId = el.getAttribute('data-sender-id') || 'unknown';
      
      return {
        id: messageId,
        senderId,
        content,
        contentLength: content.length,
        isEncrypted: content.includes('Encrypted') || content.match(/^[A-Za-z0-9+/]+=*$/),
        element: el
      };
    });
    
    // Check for changes
    const currentSnapshot = JSON.stringify(currentMessages.map(m => ({ id: m.id, content: m.content })));
    const lastSnapshot = JSON.stringify(window.debugState.uiMessages.map(m => ({ id: m.id, content: m.content })));
    
    if (currentSnapshot !== lastSnapshot) {
      debugLog('UI', `Message count: ${currentMessages.length}`, currentMessages);
      
      // Check for duplicates
      const contentCounts = {};
      currentMessages.forEach(msg => {
        contentCounts[msg.content] = (contentCounts[msg.content] || 0) + 1;
      });
      
      const duplicates = Object.entries(contentCounts).filter(([content, count]) => count > 1);
      if (duplicates.length > 0) {
        console.error('❌ [UI] DUPLICATE MESSAGES FOUND:', duplicates);
      }
      
      // Check for encrypted content in UI
      const encryptedInUI = currentMessages.filter(msg => msg.isEncrypted);
      if (encryptedInUI.length > 0) {
        console.error('❌ [UI] ENCRYPTED CONTENT IN UI:', encryptedInUI);
      }
      
      window.debugState.uiMessages = currentMessages;
    }
  };
  
  // Check UI every 200ms
  setInterval(checkUIMessages, 200);
  debugLog('UI', 'UI monitoring started');
}

// 4. Test Message Sending
function testMessageSending() {
  const messageInput = document.querySelector('[data-testid="message-input"]');
  const sendButton = document.querySelector('[data-testid="send-button"]');
  
  if (!messageInput || !sendButton) {
    console.error('❌ Message input or send button not found');
    return false;
  }
  
  const testMessage = `Debug test message - ${Date.now()}`;
  
  debugLog('TEST', 'Sending test message', testMessage);
  
  // Clear and fill input
  messageInput.value = '';
  messageInput.focus();
  messageInput.value = testMessage;
  messageInput.dispatchEvent(new Event('input', { bubbles: true }));
  
  // Click send
  setTimeout(() => {
    sendButton.click();
    debugLog('TEST', 'Send button clicked');
  }, 100);
  
  return true;
}

// 5. Generate Report
function generateReport() {
  console.log('\n📊 [REPORT] Debug Report Generated:');
  console.log('=====================================');
  
  const report = {
    duration: Date.now() - window.debugState.startTime,
    outgoingMessages: window.debugState.outgoingMessages.length,
    incomingMessages: window.debugState.incomingMessages.length,
    reduxStateChanges: window.debugState.reduxStateChanges.length,
    currentUIMessages: window.debugState.uiMessages.length,
    
    // Analysis
    duplicateUIMessages: (() => {
      const contentCounts = {};
      window.debugState.uiMessages.forEach(msg => {
        contentCounts[msg.content] = (contentCounts[msg.content] || 0) + 1;
      });
      return Object.entries(contentCounts).filter(([content, count]) => count > 1);
    })(),
    
    encryptedUIMessages: window.debugState.uiMessages.filter(msg => msg.isEncrypted),
    
    encryptionStatus: {
      outgoingEncrypted: window.debugState.outgoingMessages.filter(msg => 
        msg.data.encryptedContent && msg.data.iv && msg.data.senderRatchetKey
      ).length,
      incomingEncrypted: window.debugState.incomingMessages.filter(msg => 
        msg.data.isEncrypted
      ).length
    }
  };
  
  console.log('📊 Summary:', report);
  console.log('📤 Outgoing Messages:', window.debugState.outgoingMessages);
  console.log('📥 Incoming Messages:', window.debugState.incomingMessages);
  console.log('🟡 Redux Changes:', window.debugState.reduxStateChanges);
  console.log('🖥️ UI Messages:', window.debugState.uiMessages);
  
  // Highlight issues
  if (report.duplicateUIMessages.length > 0) {
    console.error('❌ ISSUE: Duplicate messages in UI:', report.duplicateUIMessages);
  }
  
  if (report.encryptedUIMessages.length > 0) {
    console.error('❌ ISSUE: Encrypted content displayed in UI:', report.encryptedUIMessages);
  }
  
  if (report.encryptionStatus.outgoingEncrypted === 0 && window.debugState.outgoingMessages.length > 0) {
    console.error('❌ ISSUE: No outgoing messages are encrypted');
  }
  
  return report;
}

// Initialize monitoring
console.log('🔧 [DEBUG] Initializing monitoring systems...');
monitorReduxState();
monitorSocketMessages();
monitorUIMessages();

// Expose functions globally
window.debugTest = {
  sendMessage: testMessageSending,
  generateReport: generateReport,
  getState: () => window.debugState
};

console.log('✅ [DEBUG] Debug script initialized!');
console.log('📋 [DEBUG] Available commands:');
console.log('  - debugTest.sendMessage() - Send a test message');
console.log('  - debugTest.generateReport() - Generate debug report');
console.log('  - debugTest.getState() - Get current debug state');
console.log('\n🚀 [DEBUG] Ready! Navigate to a conversation and try sending a message.');
console.log('💡 [DEBUG] Or run debugTest.sendMessage() to send a test message automatically.');
